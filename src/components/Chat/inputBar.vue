<template>
  <div
    class="input-bar"
    :class="{
      'voice-active': isRecording,
      'keyboard-mode': isKeyboardMode && !isRecording,
      'ios-keyboard-visible': isKeyboardVisible,
    }"
  >
    <!-- 键盘输入模式 -->
    <div v-if="isKeyboardMode && !isRecording" class="keyboard-wrapper">
      <div class="voice-toggle" @click="handleVoiceButtonClick">
        <i class="iconfont icon-microphone" class-prefix="icon"></i>
      </div>
      <div class="input-box">
        <van-field
          v-model="textMessage"
          class="input-content"
          rows="1"
          autosize
          autocomplete="off"
          inputmode="text"
          type="textarea"
          placeholder="可以问我任何问题"
          @keydown.enter.prevent="handleSend"
          @compositionstart="handleComposition"
          @compositionupdate="handleComposition"
          @compositionend="handleComposition"
          @input="handleInput"
        />
      </div>

      <!-- 发送按钮 -->
      <div
        class="send-icon"
        :class="{
          'not-input': textMessage === '',
          'loading-input': isLoading,
        }"
      >
        <i
          v-if="!isLoading && !props.isLoading"
          class="iconfont icon-mobile-send"
          class-prefix="icon"
          @click="handleSend"
        ></i>
        <i v-else class="iconfont icon-stop-circle-line" class-prefix="icon" @click="handleStop"></i>
      </div>
    </div>

    <!-- 语音输入模式 -->
    <div v-if="!isKeyboardMode || isRecording">
      <!-- 语音输入时的文字显示区域 -->
      <div v-if="isRecording" class="voice-text-display">
        <div v-if="!voiceMessage" class="voice-placeholder">我在听，请说...</div>
        <div v-else ref="audioMessageRef" class="voice-message-text">{{ voiceMessage }}</div>
      </div>

      <!-- 按钮区域 -->
      <div class="button-container">
        <!-- 左侧语音输入按钮 -->
        <div
          class="voice-button"
          :class="{ recording: isRecording, disabled: isLoading }"
          @touchstart.prevent="startRecording"
        >
          <div class="voice-button-bg" :class="{ recording: isRecording }"></div>
          <i class="iconfont icon-microphone voice-icon" class-prefix="icon"></i>
        </div>

        <!-- 中间语音动态线 -->
        <div v-if="isRecording" class="voice-wave">
          <div v-for="index in 30" :key="index" class="wave-line"></div>
        </div>

        <!-- 右侧Get Started按钮/键盘切换按钮 -->
        <div
          class="get-started-button"
          :class="{ 'recording-mode': isRecording, 'keyboard-icon-mode': isChatPage && !isRecording }"
          @click="handleRightButtonClick"
        >
          <!-- 首页显示"开始体验"文字 -->
          <span v-if="!isRecording && !isChatPage">开始体验</span>
          <!-- 聊天页面显示键盘图标 -->
          <i
            v-if="!isRecording && isChatPage"
            class="iconfont icon-mticon-keyboard keyboard-icon"
            class-prefix="icon"
          ></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onBeforeUnmount } from 'vue';
import { showToast, Field as vanField } from 'vant';
import { getStreamAsr } from '@/apis/chat';

import Recorder from 'recorder-realtime';
import { generateRandomString } from '@/utils';
import { debounce } from 'lodash-es';
import { useRoute } from 'vue-router';

// Props
const props = defineProps<{
  isLoading?: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'voice-send', message: string): void;
  (e: 'get-started'): void;
  (e: 'send', message: string): void;
  (e: 'update:modelValue', value: string): void;
  (e: 'stop'): void;
  (e: 'recording-status', isRecording: boolean): void;
}>();

const route = useRoute();

// 录音相关变量
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let timerId: ReturnType<typeof setTimeout> | null = null;
// 保存媒体流引用，用于释放麦克风资源
let mediaStream: MediaStream | null = null;
const audioMessageRef = ref();

// 响应式数据
const micPermission = ref(false); // 麦克风权限
const sessionId = ref(''); // 语音转文字sessionId
const audioBufferIndex = ref(0); // 语音转文字流序列号
const lastBuffer = ref(); // 语音转文字最后一条流
const voiceMessage = ref(''); // 发送的对话文字
const isRecording = ref(false); // 是否录音输入

// 新增状态
const isKeyboardMode = ref(false); // 是否为键盘输入模式
const textMessage = ref(''); // 文字输入内容
const isOnComposition = ref(false); // 是否在输入法组合状态
const hasEnteredChatPage = ref(false); // 是否已进入聊天页面

// iOS键盘适配相关状态
const isKeyboardVisible = ref(false); // 键盘是否可见
const keyboardHeight = ref(0); // 键盘高度
const originalViewportHeight = ref(0); // 原始视口高度

// 检测当前是否在聊天页面
const isChatPage = computed(() => {
  return route.name === 'chat-conversation';
});

// 设置麦克风权限（仅在需要时请求）
async function setMicPermission() {
  try {
    // 保存媒体流引用，用于后续释放资源
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
}

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    // 如果浏览器不支持录音功能，给用户提示
    showToast('录音失败，浏览器不支持录音功能');
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  // 当录音开始时的回调
  recorder.onstart = () => {};

  // 处理录音错误的回调
  recorder.onstreamerror = () => {
    // 显示录音错误消息并停止录音
    showToast('录音失败');
    cancelRecording();
  };

  // 当数据可用时的回调
  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      if (streamData.data.text) {
        voiceMessage.value = streamData.data.full_text;
        await autoSendTimeout();
        await audioMessageScroll();
      }
    }
  };
};

// 开始录音
async function startRecording() {
  if (route.meta.pageCase) {
    route.meta.pageCase('moduleClick', 'b_smartassistant_z15ks0wo_mc');
  }
  if (props.isLoading) {
    return;
  }
  if (isRecording.value) {
    await stopRecording();
    return;
  }

  // 如果没有麦克风权限，先请求权限
  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    // 通知父组件录音状态变化
    emit('recording-status', true);
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopRecording();
    }, 1000 * 60);
  }
}

// 两秒不说话自动发送
const autoSendTimeout = debounce(async () => {
  await stopRecording();
}, 2000);

// 释放麦克风资源
function releaseMicrophoneResources() {
  if (mediaStream) {
    // 停止所有媒体轨道，释放麦克风资源
    mediaStream.getTracks().forEach((track) => {
      track.stop();
      console.log('🎤 [inputBar] 释放麦克风轨道:', track.kind);
    });
    mediaStream = null;
    micPermission.value = false;
    console.log('✅ [inputBar] 麦克风资源已释放');
  }
}

// 取消录音
function cancelRecording() {
  if (recorder) {
    recorder.stop();
  }
  isRecording.value = false;
  // 通知父组件录音状态变化
  emit('recording-status', false);
  voiceMessage.value = '';
  // 释放麦克风资源
  releaseMicrophoneResources();
  handleVoiceInput();
}
async function audioMessageScroll() {
  await nextTick(() => {
    if (audioMessageRef.value) {
      audioMessageRef.value.scrollTop = audioMessageRef.value.scrollHeight;
    }
  });
}

// 结束录音
async function stopRecording() {
  if (!isRecording.value) return;
  isRecording.value = false;
  // 通知父组件录音状态变化
  emit('recording-status', false);
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null; // 重置定时器 ID
  }
  if (recorder) {
    recorder.stop();
  }

  // 释放麦克风资源
  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });
  if (voiceMessage.value) {
    // 直接发送语音识别的文字，不再填入输入框
    handleVoiceSend();
  } else {
    showToast('录音解析为空，请重新录制~');
  }
}
function handleVoiceSend() {
  emit('voice-send', voiceMessage.value);
  if (voiceMessage.value === '' || props.isLoading) {
    return;
  }
  voiceMessage.value = '';
}
function handleVoiceInput() {
  emit('voice-send', voiceMessage.value);
}

// 新增方法：处理语音按钮点击（在键盘模式下直接开始录音）
const handleVoiceButtonClick = async () => {
  if (isChatPage.value) {
    // 在聊天页面，直接开始录音，不切换模式
    await startRecording();
  } else {
    // 在首页，切换到语音输入模式
    isKeyboardMode.value = !isKeyboardMode.value;
  }
};

// 新增方法：处理文字输入
const handleInput = () => {
  emit('update:modelValue', textMessage.value);
};

// 新增方法：处理输入法组合事件
const handleComposition = (e: CompositionEvent) => {
  const { type } = e;
  isOnComposition.value = type !== 'compositionend';
};

// 新增方法：发送文字消息
const handleSend = () => {
  if (textMessage.value === '' || props.isLoading || isOnComposition.value) {
    return;
  }
  // 在键盘输入模式下，发送消息
  if (isKeyboardMode.value) {
    emit('send', textMessage.value);
    textMessage.value = '';
  }
};

// 新增方法：停止生成
const handleStop = () => {
  emit('stop');
};

// 新增方法：设置已进入聊天页面状态
const setEnteredChatPage = (entered: boolean) => {
  hasEnteredChatPage.value = entered;
};

// 新增方法：处理右侧按钮点击（开始体验/键盘切换/取消录音）
const handleRightButtonClick = () => {
  if (isRecording.value) {
    // 如果正在录音，取消录音并释放麦克风资源
    console.log('🚫 [inputBar] 用户点击取消录音');
    cancelRecording();
  } else if (isChatPage.value) {
    // 在聊天页面，切换到键盘输入模式
    isKeyboardMode.value = true;
  } else {
    // 在首页，触发开始体验事件
    emit('get-started');
  }
};

// iOS键盘适配相关函数
const setupKeyboardAdaptation = () => {
  // 检测是否为iOS设备
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
  if (!isIOS) return;

  // 记录原始视口高度
  originalViewportHeight.value = window.innerHeight;

  // 优先使用Visual Viewport API（更准确）
  const handleVisualViewportChange = () => {
    if (window.visualViewport) {
      const viewport = window.visualViewport;
      const heightDiff = window.screen.height - viewport.height;

      if (heightDiff > 150) {
        isKeyboardVisible.value = true;
        keyboardHeight.value = heightDiff;
        console.log('🎹 [inputBar] iOS键盘弹起（Visual Viewport），高度差:', heightDiff);

        // 为body添加键盘可见的类
        document.body.classList.add('ios-keyboard-visible');

        // 立即滚动到输入框，减少延迟
        scrollToInput();
        // 再次确保滚动到位
        setTimeout(() => {
          scrollToInput();
        }, 100);
      } else {
        isKeyboardVisible.value = false;
        keyboardHeight.value = 0;
        console.log('🎹 [inputBar] iOS键盘收起（Visual Viewport）');

        // 移除body的键盘可见类
        document.body.classList.remove('ios-keyboard-visible');
      }
    }
  };

  // 备用方案：监听视口高度变化
  const handleResize = () => {
    // 如果有Visual Viewport API，优先使用它
    if (window.visualViewport) return;

    const currentHeight = window.innerHeight;
    const heightDiff = originalViewportHeight.value - currentHeight;

    // 如果高度差超过150px，认为键盘弹起了
    if (heightDiff > 150) {
      isKeyboardVisible.value = true;
      keyboardHeight.value = heightDiff;
      console.log('🎹 [inputBar] iOS键盘弹起（Resize），高度差:', heightDiff);

      // 为body添加键盘可见的类
      document.body.classList.add('ios-keyboard-visible');

      // 立即滚动到输入框，减少延迟
      scrollToInput();
      showSuccessToast
      // 再次确保滚动到位
      setTimeout(() => {
        scrollToInput();
      }, 100);
    } else {
      isKeyboardVisible.value = false;
      keyboardHeight.value = 0;
      console.log('🎹 [inputBar] iOS键盘收起（Resize）');

      // 移除body的键盘可见类
      document.body.classList.remove('ios-keyboard-visible');
    }
  };

  // 监听输入框聚焦事件
  const handleInputFocus = () => {
    console.log('🎯 [inputBar] 输入框获得焦点');
    // 立即处理，减少延迟
    scrollToInput();
    // 短延迟再次确保
    setTimeout(() => {
      scrollToInput();
    }, 150);
  };

  // 监听输入框失焦事件
  const handleInputBlur = () => {
    console.log('🎯 [inputBar] 输入框失去焦点');
  };

  // 滚动到输入框位置 - 优化iOS键盘适配
  const scrollToInput = () => {
    const inputElement = document.querySelector('.input-content .van-field__control');
    const footerElement = document.querySelector('.footer');

    if (inputElement && footerElement) {
      // 使用更智能的滚动策略
      const inputRect = inputElement.getBoundingClientRect();
      const footerRect = footerElement.getBoundingClientRect();
      let viewportHeight = window.innerHeight;

      // 如果有Visual Viewport API，使用它获取更准确的可视区域高度
      if (window.visualViewport) {
        viewportHeight = window.visualViewport.height;
      }

      console.log('📜 [inputBar] 滚动检查:', {
        inputBottom: inputRect.bottom,
        footerTop: footerRect.top,
        viewportHeight,
        keyboardHeight: keyboardHeight.value,
      });

      // 计算输入框和预设问题区域是否被键盘遮挡
      const targetPosition = footerRect.top; // footer顶部位置
      const availableSpace = viewportHeight - 50; // 留50px缓冲区
      const isObscured = targetPosition > availableSpace;

      if (isObscured) {
        // 计算需要滚动的距离，确保整个footer区域（包括预设问题）都可见
        const scrollOffset = targetPosition - availableSpace + 50;

        console.log('📜 [inputBar] 需要滚动，偏移量:', scrollOffset);

        // 方案1：尝试使用scrollIntoView滚动footer
        try {
          footerElement.scrollIntoView({
            behavior: 'smooth',
            block: 'end', // 滚动到底部
            inline: 'nearest',
          });
          console.log('📜 [inputBar] 使用scrollIntoView滚动到footer');
        } catch (error) {
          // 方案2：备用滚动方案
          window.scrollBy({
            top: scrollOffset,
            behavior: 'smooth',
          });
          console.log('📜 [inputBar] 使用scrollBy滚动，偏移量:', scrollOffset);
        }
      } else {
        console.log('📜 [inputBar] 输入框区域未被遮挡，无需滚动');
      }
    }
  };

  // 添加事件监听
  // 优先使用Visual Viewport API
  if (window.visualViewport) {
    window.visualViewport.addEventListener('resize', handleVisualViewportChange);
  }
  // 备用方案：监听window resize
  window.addEventListener('resize', handleResize);

  // 监听输入框的聚焦和失焦事件
  void nextTick(() => {
    const inputElement = document.querySelector('.input-content .van-field__control');
    if (inputElement) {
      inputElement.addEventListener('focus', handleInputFocus);
      inputElement.addEventListener('blur', handleInputBlur);
    }
  });

  // 返回清理函数
  return () => {
    if (window.visualViewport) {
      window.visualViewport.removeEventListener('resize', handleVisualViewportChange);
    }
    window.removeEventListener('resize', handleResize);
    const inputElement = document.querySelector('.input-content .van-field__control');
    if (inputElement) {
      inputElement.removeEventListener('focus', handleInputFocus);
      inputElement.removeEventListener('blur', handleInputBlur);
    }

    // 清理时移除body的键盘可见类
    document.body.classList.remove('ios-keyboard-visible');
  };
};

// 暴露方法给父组件使用
defineExpose({
  setEnteredChatPage,
});

// 移除自动权限请求，改为在需要时请求
// onBeforeMount(async () => {
//   await setMicPermission();
// });

// 键盘适配清理函数
let keyboardCleanup: (() => void) | null = null;

onMounted(() => {
  console.log('🎬 [inputBar] 组件挂载，开始初始化');

  // 如果在聊天页面，默认设置为键盘输入模式
  if (isChatPage.value) {
    console.log('🎯 [inputBar] 检测到聊天页面，设置为键盘输入模式');
    isKeyboardMode.value = true;
  }

  // 设置iOS键盘适配
  const cleanup = setupKeyboardAdaptation();
  if (cleanup) {
    keyboardCleanup = cleanup;
  }
});

// 组件卸载时释放麦克风资源
onBeforeUnmount(() => {
  console.log('🧹 [inputBar] 组件卸载，释放麦克风资源');
  if (isRecording.value) {
    // 如果正在录音，先停止录音
    if (recorder) {
      recorder.stop();
    }
    isRecording.value = false;
    // 通知父组件录音状态变化
    emit('recording-status', false);
  }
  // 释放麦克风资源
  releaseMicrophoneResources();

  // 清理键盘适配
  if (keyboardCleanup) {
    keyboardCleanup();
    keyboardCleanup = null;
  }
});
</script>

<style scoped lang="scss">
.input-bar {
  width: 100%;
  min-height: 170px; // 增加默认高度，为更大的按钮留出空间
  background: transparent; // 默认透明背景，去除毛玻璃效果
  border-radius: 20px 20px 0px 0px;
  padding: 28px 40px; // 增加上下padding
  box-sizing: border-box;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 36px);
  padding-bottom: calc(env(safe-area-inset-bottom) + 36px);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  transition: all 0.3s ease;
  position: relative;

  // 录音状态下恢复原来的高度和背景效果
  &.voice-active {
    min-height: 300px;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.1) 60%, rgba(0, 0, 0, 0.3) 100%);
    // backdrop-filter: blur(10px);
  }

  &.keyboard-mode {
    min-height: auto;
    background: var(--bg-glass);
    border: 2px solid var(--border-glass);
    backdrop-filter: blur(20px);
    padding: 16px 23px;
    box-shadow: var(--shadow-strong);

    // iOS Safari 兼容性修复 - 保持与其他平台一致的颜色
    @supports (-webkit-touch-callout: none) {
      // 使用与其他平台相同的背景色和边框色
      background: var(--bg-glass) !important;
      border-color: var(--border-glass) !important;
      // iOS上的毛玻璃效果兼容性
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
    }

    // iOS键盘弹起时的适配
    @supports (-webkit-touch-callout: none) {
      // iOS设备特有的样式
      position: relative;
      z-index: 1000;

      // 确保输入框在键盘弹起时可见
      .keyboard-wrapper {
        .input-box {
          .input-content {
            // 防止iOS键盘弹起时输入框被压缩
            min-height: 88px !important;

            // 确保输入框内容可见
            :deep(.van-field__control) {
              -webkit-user-select: text;
              user-select: text;
              -webkit-appearance: none;
              appearance: none;
            }
          }
        }
      }
    }
  }

  // iOS键盘可见状态的样式
  &.ios-keyboard-visible {
    // 键盘弹起时的特殊样式
    @supports (-webkit-touch-callout: none) {
      // 确保输入框区域始终可见
      .keyboard-wrapper {
        .input-box {
          .input-content {
            // 键盘弹起时保持输入框的最小高度
            min-height: 88px !important;

            // 确保输入框内容不被压缩
            :deep(.van-field__control) {
              min-height: 40px !important;
              font-size: max(16px, var(--font-size-2xl)) !important;
            }
          }
        }
      }
    }
  }

  // 键盘输入模式样式
  .keyboard-wrapper {
    display: flex;
    align-items: flex-end;
    width: 100%;

    .voice-toggle {
      width: 56px;
      height: 56px;
      display: flex;
      flex-shrink: 0;
      margin-bottom: 20px;
      box-sizing: border-box;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
      }

      .iconfont {
        font-size: 56px;
        color: var(--text-primary);
      }
    }

    .input-box {
      flex: 1;
      display: flex;
      align-items: stretch;
      margin-left: 20px; // 减少左边距
      margin-right: 16px; // 增加右边距
      .input-content {
        width: 100%;
        max-height: 192px;
        min-height: 88px;
        padding: 24px 20px !important;
        box-sizing: border-box;
        border-radius: var(--border-radius-xl);
        background: var(--bg-glass);
        border: 2px solid var(--border-accent);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
        box-shadow: var(--shadow-medium);

        // iOS Safari 兼容性修复 - 保持与其他平台一致的颜色
        @supports (-webkit-touch-callout: none) {
          // 使用与其他平台相同的背景色和边框色
          background: var(--bg-glass) !important;
          border-color: var(--border-accent) !important;
          // iOS上的毛玻璃效果兼容性
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
        }

        &:focus-within {
          border-color: var(--accent-color);
          box-shadow:
            0 0 0 3px var(--accent-color-light),
            var(--shadow-strong);
          transform: translateY(-2px);
        }

        :deep(.van-field__control) {
          color: var(--text-primary);
          font-size: var(--font-size-2xl) !important;
          line-height: 1.4;
          max-height: 165px;
          font-weight: 500;
        }

        :deep(.van-field__control::placeholder) {
          color: var(--text-quaternary);
          font-size: var(--font-size-xl) !important;
          font-weight: 400;
        }
      }
    }

    .send-icon {
      width: 68px;
      height: 68px;
      flex-shrink: 0;
      background: var(--primary-color-light);
      border: 2px solid var(--primary-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: var(--shadow-strong);

      &:hover {
        background: var(--primary-color-medium);
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 24px var(--primary-color-strong);
      }

      &.not-input {
        background: var(--bg-glass);
        border-color: var(--border-light);
        .iconfont {
          color: var(--text-disabled);
        }

        &:hover {
          transform: none;
          box-shadow: var(--shadow-medium);
        }
      }

      &.loading-input {
        background: var(--accent-color-light);
        border-color: var(--accent-color);
        .iconfont {
          font-size: 60px;
          color: var(--accent-color);
        }
      }

      .icon-mobile-send {
        font-size: 72px; // 进一步增大发送图标
        margin-left: 4px;
        color: var(--primary-color);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .voice-text-display {
    height: 100px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: center;

    .voice-placeholder {
      color: var(--text-secondary);
      font-size: 20px;
      font-weight: 500;
      opacity: 0.7;
      text-align: center;
    }

    .voice-message-text {
      color: var(--text-primary);
      font-size: 16px;
      font-weight: 400;
      line-height: 1.5;
      text-align: center;
      max-height: 110px; // 相应增加最大高度
      overflow-y: auto;
    }
  }

  .button-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 60px;
    position: relative;

    .voice-wave {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      height: 40px;
      margin: 0 15px;

      .wave-line {
        width: 3px;
        height: 15px;
        border-radius: 3px;

        &:nth-child(5n + 1) {
          background-color: rgba(255, 255, 255, 0.9);
          animation: wave1 0.5s infinite ease-in-out alternate;
        }
        &:nth-child(5n + 2) {
          background-color: rgba(255, 255, 255, 0.7);
          animation: wave2 0.6s infinite ease-in-out alternate;
        }
        &:nth-child(5n + 3) {
          background-color: rgba(255, 255, 255, 0.8);
          animation: wave3 0.5s infinite ease-in-out alternate;
        }
        &:nth-child(5n + 4) {
          background-color: rgba(255, 255, 255, 0.6);
          animation: wave4 0.7s infinite ease-in-out alternate;
        }
        &:nth-child(5n) {
          background-color: rgba(255, 255, 255, 0.95);
          animation: wave5 0.55s infinite ease-in-out alternate;
        }

        &:nth-child(1) {
          animation-delay: 0s;
        }
        &:nth-child(2) {
          animation-delay: 0.05s;
        }
        &:nth-child(3) {
          animation-delay: 0.1s;
        }
        &:nth-child(4) {
          animation-delay: 0.15s;
        }
        &:nth-child(5) {
          animation-delay: 0.2s;
        }
        &:nth-child(6) {
          animation-delay: 0.25s;
        }
        &:nth-child(7) {
          animation-delay: 0.3s;
        }
        &:nth-child(8) {
          animation-delay: 0.35s;
        }
        &:nth-child(9) {
          animation-delay: 0.4s;
        }
        &:nth-child(10) {
          animation-delay: 0.45s;
        }
      }
    }

    .voice-button {
      width: 84px; // 进一步增大按钮尺寸
      height: 84px;
      border-radius: 50%;
      background: var(--bg-glass);
      border: 2px solid var(--border-glass);
      backdrop-filter: blur(20px);

      // iOS Safari 兼容性修复 - 保持与其他平台一致的颜色
      @supports (-webkit-touch-callout: none) {
        background: var(--bg-glass) !important;
        border-color: var(--border-glass) !important;
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
      }
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      flex-shrink: 0;
      position: relative;
      box-shadow: var(--shadow-strong);

      &:hover {
        background: var(--bg-glass-hover);
        border-color: var(--border-accent);
        transform: translateY(-2px);
      }

      &.disabled {
        opacity: 0.5;
        pointer-events: none;
      }

      .voice-button-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        transition: all 0.3s ease;

        &.recording {
          background: radial-gradient(circle at center, #ffffff 0%, #f0f0f0 30%, #e0e0e0 60%, #000000 100%);
          border: 1px solid #666666;
          animation: voiceRecording 2s ease-in-out infinite;
          box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.3);
        }
      }

      .voice-icon {
        font-size: 42px; // 增大语音图标
        color: var(--text-primary);
        position: relative;
        z-index: 2;
        transition: color 0.3s ease;
      }

      &.recording .voice-icon {
        color: #000000;
      }

      &:active {
        transform: scale(0.95);
      }
    }

    .get-started-button {
      flex: 0 0 auto;
      min-width: 200px; // 进一步增大最小宽度
      max-width: 200px; // 进一步增大最大宽度
      width: fit-content;
      height: 80px; // 增大高度
      padding: 0 36px; // 增大padding
      border-radius: 40px; // 相应调整圆角
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      position: relative;
      z-index: 2;

      // 主要按钮样式
      color: var(--primary-color);
      border: 2px solid var(--primary-color);
      background: var(--primary-color-light);
      font-size: 18px;
      font-weight: 600;
      cursor: pointer;

      &:hover {
        background: var(--primary-color-medium);
        transform: translateY(-2px);
        box-shadow: 0 8px 24px var(--primary-color-strong);
      }

      // 聊天页面键盘图标模式下的样式
      &.keyboard-icon-mode {
        min-width: 84px; // 进一步增大最小宽度
        max-width: 84px;
        width: 84px;
        height: 84px; // 增大高度
        padding: 0;
      }

      span {
        color: var(--text-primary);
        font-size: 16px;
        font-weight: 600;
        line-height: 1.5;
        letter-spacing: 0.5px;
        transition: opacity 0.3s ease;
      }

      .keyboard-icon {
        font-size: 38px; // 增大键盘图标
        color: var(--text-primary);
      }

      // 录音模式下的样式
      &.recording-mode {
        width: 80px; // 增大录音模式下的按钮
        height: 80px;
        border-radius: 50%;
        min-width: 80px;
        max-width: 80px;
        padding: 0;
        background: var(--bg-glass);
        border: 2px solid var(--border-accent);
        backdrop-filter: blur(20px);
        box-shadow: var(--shadow-strong), var(--shadow-accent);

        // iOS Safari 兼容性修复 - 保持与其他平台一致的颜色
        @supports (-webkit-touch-callout: none) {
          background: var(--bg-glass) !important;
          border-color: var(--border-accent) !important;
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
        }

        span {
          opacity: 0;
        }
      }

      &:active {
        transform: scale(0.98);
      }
    }
  }
}

@keyframes voiceRecording {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 var(--accent-color-light);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px var(--accent-color-medium);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 20px rgba(0, 255, 255, 0);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .input-bar {
    padding: 20px 20px; // 增加padding
    min-height: 120px; // 增加最小高度

    // iOS设备特殊适配
    @supports (-webkit-touch-callout: none) {
      // 确保在iOS设备上有足够的底部安全区域
      padding-bottom: calc(env(safe-area-inset-bottom) + 20px);

      // 键盘模式下的特殊处理
      &.keyboard-mode {
        // 键盘弹起时保持固定位置
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;

        // 确保背景不透明，避免内容透过
        background: var(--bg-glass);
        backdrop-filter: blur(20px);

        // iOS Safari 兼容性修复 - 保持与其他平台一致的颜色
        @supports (-webkit-touch-callout: none) {
          background: var(--bg-glass) !important;
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
        }
      }
    }

    .keyboard-wrapper {
      .voice-toggle {
        width: 48px; // 增大移动端语音切换按钮
        height: 48px;
        margin-bottom: 16px;

        .iconfont {
          font-size: 48px; // 增大图标
        }
      }

      .input-box {
        margin-left: 12px; // 调整边距
        margin-right: 12px;

        .input-content {
          min-height: 76px; // 增加输入框高度
          padding: 20px 16px !important;
          border-radius: var(--border-radius-lg);

          :deep(.van-field__control) {
            font-size: var(--font-size-xl) !important;
            line-height: 1.4;

            // iOS输入法适配
            @supports (-webkit-touch-callout: none) {
              // 防止iOS输入法导致的样式问题
              -webkit-appearance: none;
              appearance: none;
              -webkit-user-select: text;
              user-select: text;

              // 确保输入框在键盘弹起时保持正确的尺寸
              min-height: 40px;
              resize: none;

              // 防止iOS缩放
              font-size: max(16px, var(--font-size-xl)) !important;
            }
          }

          :deep(.van-field__control::placeholder) {
            font-size: 24px;

            // iOS设备上确保占位符字体大小不会导致缩放
            @supports (-webkit-touch-callout: none) {
              font-size: max(16px, 24px);
            }
          }
        }
      }

      .send-icon {
        width: 64px; // 增大发送按钮
        height: 64px;
        margin-bottom: 6px;

        .icon-mobile-send {
          font-size: 56px; // 增大发送图标
        }
      }
    }

    .button-container {
      height: 68px; // 增加按钮容器高度

      .voice-button {
        width: 64px; // 增大语音按钮
        height: 64px;

        .voice-icon {
          font-size: 36px; // 增大语音图标
        }
      }

      .get-started-button {
        &.recording-mode {
          width: 64px;
          height: 64px;
        }

        &.keyboard-icon-mode {
          width: 64px;
          height: 64px;

          .keyboard-icon {
            font-size: 32px; // 增大键盘图标
          }
        }

        &:not(.recording-mode):not(.keyboard-icon-mode) {
          min-width: 160px; // 增大Get Started按钮宽度
          height: 64px; // 增大高度
          padding: 0 24px;

          span {
            font-size: 18px; // 增大文字
          }
        }
      }
    }
  }
}

@keyframes wave1 {
  0% {
    height: 5px;
  }
  100% {
    height: 20px;
  }
}

@keyframes wave2 {
  0% {
    height: 8px;
  }
  100% {
    height: 32px;
  }
}

@keyframes wave3 {
  0% {
    height: 3px;
  }
  100% {
    height: 25px;
  }
}

@keyframes wave4 {
  0% {
    height: 10px;
  }
  100% {
    height: 28px;
  }
}

// 全局iOS键盘适配样式
:global(body.ios-keyboard-visible) {
  // iOS键盘弹起时的全局样式
  @supports (-webkit-touch-callout: none) {
    // 关键修复：让页面能被键盘正确顶起
    // 使用viewport单位确保页面高度适应键盘

    // 调整页面容器高度为可视区域高度
    #app {
      height: 100vh;
      height: calc(var(--vh, 1vh) * 100);
      // 允许页面正常滚动，让键盘能顶起内容
      overflow: visible;
    }

    // 确保预设问题在键盘弹起时仍然可见且正确定位
    .preset-questions-container {
      z-index: 1002 !important;
      -webkit-transform: translateZ(0) !important;
      transform: translateZ(0) !important;
      // 强制显示
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
      // 快速过渡
      transition: all 0.1s ease !important;
      // 确保在键盘弹起时立即可见
      position: relative !important;
      min-height: 80px !important;
    }

    // 聊天页面特殊处理 - 让页面能被键盘顶起
    .v-chat-container {
      // 使用可视区域高度，让键盘能正确顶起页面
      height: 100vh;
      height: calc(var(--vh, 1vh) * 100);
      // 允许内容被键盘顶起
      position: relative;

      .v-chat {
        height: 100%;
        display: flex;
        flex-direction: column;

        .chat-content {
          flex: 1;
          // 确保聊天内容区域能正确滚动
          overflow: hidden;

          .chat-scroll-wrapper {
            // 键盘弹起时，减少底部padding，为footer让出空间
            padding-bottom: 200px !important;
          }
        }

        .footer {
          // 不使用fixed定位，让footer能被键盘顶起
          position: relative !important;
          flex-shrink: 0;
          z-index: 1003 !important;

          // 确保footer有背景，不透明
          background: rgba(0, 0, 0, 0.9) !important;
          backdrop-filter: blur(20px) !important;
          -webkit-backdrop-filter: blur(20px) !important;

          // 添加上边框，增强视觉分离
          border-top: 1px solid rgba(255, 255, 255, 0.1) !important;

          // 快速过渡，减少延迟感
          transition: all 0.1s ease !important;

          // 确保预设问题组件在footer内可见
          .preset-questions-container {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            min-height: 80px !important;
            background: rgba(255, 255, 0, 0.1) !important; // 临时调试用黄色背景
          }
        }
      }
    }
  }
}

@keyframes wave5 {
  0% {
    height: 6px;
  }
  100% {
    height: 35px;
  }
}
</style>
